import React from 'react';
import StreamGrid from '../components/Stream/StreamGrid';
import CategorySection from '../components/Category/CategorySection';

const HomePage: React.FC = () => {
  return (
    <div className="p-6 pt-20 animate-fadeIn">
      <div className="mb-8">
        <h1 className="text-3xl font-bold gradient-text mb-2">Live Streams</h1>
        <p className="text-gray-400">Discover amazing content from streamers around the world</p>
      </div>

      <div className="mb-12">
        <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
          <span>🔥</span>
          <span>Trending Now</span>
        </h2>
        <StreamGrid />
      </div>

      <div className="mb-12">
        <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
          <span>🎯</span>
          <span>Browse Categories</span>
        </h2>
        <CategorySection />
      </div>

      <div>
        <h2 className="text-xl font-semibold text-white mb-4 flex items-center space-x-2">
          <span>⭐</span>
          <span>Recommended for You</span>
        </h2>
        <StreamGrid />
      </div>
    </div>
  );
};

export default HomePage;
