import React from 'react';
import CategoryCard from './CategoryCard';

const CategorySection: React.FC = () => {
  const categories = [
    {
      name: 'Gaming',
      viewers: '1.2M',
      thumbnail: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=300&h=400&fit=crop&crop=center',
      path: '/category/gaming'
    },
    {
      name: 'Music',
      viewers: '234K',
      thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=400&fit=crop&crop=center',
      path: '/category/music'
    },
    {
      name: 'Art',
      viewers: '89K',
      thumbnail: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=400&fit=crop&crop=center',
      path: '/category/art'
    },
    {
      name: 'Talk Shows',
      viewers: '156K',
      thumbnail: 'https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=300&h=400&fit=crop&crop=center',
      path: '/category/talk'
    },
    {
      name: 'Sports',
      viewers: '445K',
      thumbnail: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=300&h=400&fit=crop&crop=center',
      path: '/category/sports'
    },
    {
      name: 'Cooking',
      viewers: '67K',
      thumbnail: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=300&h=400&fit=crop&crop=center',
      path: '/category/cooking'
    },
    {
      name: 'Fitness',
      viewers: '89K',
      thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=400&fit=crop&crop=center',
      path: '/category/fitness'
    },
    {
      name: 'Technology',
      viewers: '156K',
      thumbnail: 'https://images.unsplash.com/photo-1547394765-185e1e68f34e?w=300&h=400&fit=crop&crop=center',
      path: '/category/technology'
    }
  ];

  return (
    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
      {categories.map((category) => (
        <CategoryCard key={category.name} category={category} />
      ))}
    </div>
  );
};

export default CategorySection;
