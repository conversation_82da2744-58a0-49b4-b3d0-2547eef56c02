import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { 
  FunnelIcon, 
  Bars3Icon, 
  Squares2X2Icon,
  MagnifyingGlassIcon 
} from '@heroicons/react/24/outline';

interface Category {
  id: string;
  name: string;
  viewers: number;
  thumbnail: string;
  path: string;
  tags: string[];
  description: string;
}

const CategoryBrowser: React.FC = () => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'viewers' | 'name' | 'trending'>('viewers');
  const [filterQuery, setFilterQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // Mock categories data with beautiful images
  const categories: Category[] = [
    {
      id: 'gaming',
      name: 'Gaming',
      viewers: 1200000,
      thumbnail: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=300&h=400&fit=crop&crop=center',
      path: '/category/gaming',
      tags: ['Popular', 'Competitive', 'Entertainment'],
      description: 'The most popular gaming content from around the world'
    },
    {
      id: 'sports',
      name: 'Sports',
      viewers: 445000,
      thumbnail: 'https://images.unsplash.com/photo-1461896836934-ffe607ba8211?w=300&h=400&fit=crop&crop=center',
      path: '/category/sports',
      tags: ['Live', 'Popular', 'Events'],
      description: 'Live sports coverage and sports talk shows'
    },
    {
      id: 'music',
      name: 'Music',
      viewers: 234000,
      thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=300&h=400&fit=crop&crop=center',
      path: '/category/music',
      tags: ['Creative', 'Live Performance', 'Entertainment'],
      description: 'Live music performances and music creation'
    },
    {
      id: 'talk',
      name: 'Talk Shows',
      viewers: 156000,
      thumbnail: 'https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=300&h=400&fit=crop&crop=center',
      path: '/category/talk',
      tags: ['Discussion', 'Entertainment', 'Interactive'],
      description: 'Talk shows, podcasts, and interactive discussions'
    },
    {
      id: 'art',
      name: 'Art',
      viewers: 89000,
      thumbnail: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=300&h=400&fit=crop&crop=center',
      path: '/category/art',
      tags: ['Creative', 'Educational', 'Relaxing'],
      description: 'Digital art creation, tutorials, and creative processes'
    },
    {
      id: 'cooking',
      name: 'Cooking',
      viewers: 67000,
      thumbnail: 'https://images.unsplash.com/photo-**********-f6e7ad7d3136?w=300&h=400&fit=crop&crop=center',
      path: '/category/cooking',
      tags: ['Educational', 'Lifestyle', 'Interactive'],
      description: 'Cooking shows, recipes, and culinary adventures'
    },
    {
      id: 'fitness',
      name: 'Fitness',
      viewers: 45000,
      thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=300&h=400&fit=crop&crop=center',
      path: '/category/fitness',
      tags: ['Health', 'Educational', 'Motivational'],
      description: 'Workout sessions, fitness tips, and health advice'
    },
    {
      id: 'technology',
      name: 'Technology',
      viewers: 38000,
      thumbnail: 'https://images.unsplash.com/photo-**********-185e1e68f34e?w=300&h=400&fit=crop&crop=center',
      path: '/category/technology',
      tags: ['Educational', 'News', 'Reviews'],
      description: 'Tech reviews, programming, and technology discussions'
    },
    {
      id: 'travel',
      name: 'Travel & Outdoors',
      viewers: 52000,
      thumbnail: 'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=300&h=400&fit=crop&crop=center',
      path: '/category/travel',
      tags: ['Adventure', 'Educational', 'Relaxing'],
      description: 'Travel vlogs, outdoor adventures, and nature exploration'
    },
    {
      id: 'education',
      name: 'Education',
      viewers: 34000,
      thumbnail: 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=300&h=400&fit=crop&crop=center',
      path: '/category/education',
      tags: ['Educational', 'Learning', 'Academic'],
      description: 'Educational content, tutorials, and learning sessions'
    }
  ];

  // Get all unique tags
  const allTags = Array.from(new Set(categories.flatMap(cat => cat.tags)));

  const formatViewers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const filteredAndSortedCategories = categories
    .filter(category => {
      const matchesQuery = category.name.toLowerCase().includes(filterQuery.toLowerCase()) ||
                          category.description.toLowerCase().includes(filterQuery.toLowerCase());
      const matchesTags = selectedTags.length === 0 || 
                         selectedTags.some(tag => category.tags.includes(tag));
      return matchesQuery && matchesTags;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case 'name':
          return a.name.localeCompare(b.name);
        case 'trending':
          // Mock trending logic - could be based on growth rate
          return b.viewers - a.viewers;
        case 'viewers':
        default:
          return b.viewers - a.viewers;
      }
    });

  const toggleTag = (tag: string) => {
    setSelectedTags(prev => 
      prev.includes(tag) 
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-white mb-2">Browse Categories</h1>
        <p className="text-gray-400">Discover content across all categories</p>
      </div>

      {/* Filters and Controls */}
      <div className="bg-dark-200 rounded-lg p-4 space-y-4">
        {/* Search and View Controls */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="flex-1 relative">
            <input
              type="text"
              placeholder="Search categories..."
              value={filterQuery}
              onChange={(e) => setFilterQuery(e.target.value)}
              className="w-full bg-dark-100 border border-gray-600 rounded-lg pl-10 pr-4 py-2 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            />
            <MagnifyingGlassIcon className="absolute left-3 top-2.5 h-5 w-5 text-gray-400" />
          </div>
          
          <div className="flex items-center space-x-2">
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'viewers' | 'name' | 'trending')}
              className="bg-dark-100 border border-gray-600 rounded-lg px-3 py-2 text-white focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="viewers">Sort by Viewers</option>
              <option value="name">Sort by Name</option>
              <option value="trending">Sort by Trending</option>
            </select>
            
            <div className="flex border border-gray-600 rounded-lg overflow-hidden">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 ${viewMode === 'grid' ? 'bg-purple-600 text-white' : 'bg-dark-100 text-gray-400 hover:text-white'} transition-colors`}
              >
                <Squares2X2Icon className="h-5 w-5" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 ${viewMode === 'list' ? 'bg-purple-600 text-white' : 'bg-dark-100 text-gray-400 hover:text-white'} transition-colors`}
              >
                <Bars3Icon className="h-5 w-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Tag Filters */}
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <FunnelIcon className="h-4 w-4 text-gray-400" />
            <span className="text-sm text-gray-400">Filter by tags:</span>
          </div>
          <div className="flex flex-wrap gap-2">
            {allTags.map(tag => (
              <button
                key={tag}
                onClick={() => toggleTag(tag)}
                className={`px-3 py-1 rounded-full text-sm transition-colors ${
                  selectedTags.includes(tag)
                    ? 'bg-purple-600 text-white'
                    : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                }`}
              >
                {tag}
              </button>
            ))}
            {selectedTags.length > 0 && (
              <button
                onClick={() => setSelectedTags([])}
                className="px-3 py-1 rounded-full text-sm bg-red-600 text-white hover:bg-red-700 transition-colors"
              >
                Clear All
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Results Count */}
      <div className="text-gray-400 text-sm">
        Showing {filteredAndSortedCategories.length} of {categories.length} categories
      </div>

      {/* Categories Grid/List */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
          {filteredAndSortedCategories.map((category) => (
            <Link key={category.id} to={category.path} className="group">
              <div className="card overflow-hidden hover:scale-105 transition-transform duration-200">
                <div className="aspect-[3/4] relative">
                  <img
                    src={category.thumbnail}
                    alt={category.name}
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent" />
                  <div className="absolute bottom-4 left-4 right-4">
                    <h3 className="text-white font-semibold text-lg group-hover:text-purple-400 transition-colors">
                      {category.name}
                    </h3>
                    <p className="text-gray-300 text-sm">{formatViewers(category.viewers)} viewers</p>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      ) : (
        <div className="space-y-3">
          {filteredAndSortedCategories.map((category) => (
            <Link key={category.id} to={category.path} className="block">
              <div className="card p-4 hover:bg-dark-100 transition-colors">
                <div className="flex items-center space-x-4">
                  <img
                    src={category.thumbnail}
                    alt={category.name}
                    className="w-16 h-16 rounded-lg object-cover"
                  />
                  <div className="flex-1">
                    <h3 className="text-white font-semibold text-lg mb-1">{category.name}</h3>
                    <p className="text-gray-400 text-sm mb-2">{category.description}</p>
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {category.tags.map(tag => (
                          <span key={tag} className="px-2 py-1 bg-gray-700 text-gray-300 text-xs rounded">
                            {tag}
                          </span>
                        ))}
                      </div>
                      <span className="text-purple-400 font-medium">
                        {formatViewers(category.viewers)} viewers
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>
      )}

      {filteredAndSortedCategories.length === 0 && (
        <div className="text-center py-12">
          <MagnifyingGlassIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-white font-medium mb-2">No categories found</h3>
          <p className="text-gray-400">Try adjusting your search or filters</p>
        </div>
      )}
    </div>
  );
};

export default CategoryBrowser;
