import React from 'react';
import StreamCard from './StreamCard';

interface StreamGridProps {
  category?: string;
}

const StreamGrid: React.FC<StreamGridProps> = ({ category }) => {
  // Mock data with realistic gaming/streaming thumbnails
  const streams = [
    {
      id: '1',
      title: 'Epic Gaming Session - Come Join!',
      streamer: 'ninja',
      game: 'Fortnite',
      viewers: 45000,
      thumbnail: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'gaming'
    },
    {
      id: '2',
      title: 'Chill Music Stream 🎵',
      streamer: 'musiclover',
      game: 'Music & Performing Arts',
      viewers: 12000,
      thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'music'
    },
    {
      id: '3',
      title: 'Digital Art Creation Live',
      streamer: 'artmaster',
      game: 'Art',
      viewers: 8500,
      thumbnail: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'art'
    },
    {
      id: '4',
      title: 'Valorant Ranked Grind',
      streamer: 'pokimane',
      game: 'Valorant',
      viewers: 23000,
      thumbnail: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'gaming'
    },
    {
      id: '5',
      title: 'GTA V Roleplay Adventures',
      streamer: 'xqc',
      game: 'Grand Theft Auto V',
      viewers: 67000,
      thumbnail: 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'gaming'
    },
    {
      id: '6',
      title: 'Late Night Talk Show',
      streamer: 'talkhost',
      game: 'Just Chatting',
      viewers: 15000,
      thumbnail: 'https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'talk'
    },
    {
      id: '7',
      title: 'Minecraft Building Stream',
      streamer: 'builder123',
      game: 'Minecraft',
      viewers: 9800,
      thumbnail: 'https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'gaming'
    },
    {
      id: '8',
      title: 'Piano Performance Live',
      streamer: 'pianist',
      game: 'Music & Performing Arts',
      viewers: 5600,
      thumbnail: 'https://images.unsplash.com/photo-1520523839897-bd0b52f945a0?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'music'
    },
    {
      id: '9',
      title: 'Cooking with Chef Marco',
      streamer: 'chefmarco',
      game: 'Cooking',
      viewers: 7200,
      thumbnail: 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'cooking'
    },
    {
      id: '10',
      title: 'Morning Workout Session',
      streamer: 'fitnessguru',
      game: 'Fitness',
      viewers: 3400,
      thumbnail: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'fitness'
    },
    {
      id: '11',
      title: 'Tech Review: Latest Gaming Setup',
      streamer: 'techreviewer',
      game: 'Technology',
      viewers: 5800,
      thumbnail: 'https://images.unsplash.com/photo-1547394765-185e1e68f34e?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'technology'
    },
    {
      id: '12',
      title: 'Chess Grandmaster Tournament',
      streamer: 'chessmasterx',
      game: 'Chess',
      viewers: 12500,
      thumbnail: 'https://images.unsplash.com/photo-1529699211952-734e80c4d42b?w=400&h=225&fit=crop&crop=center',
      isLive: true,
      category: 'gaming'
    }
  ];

  const filteredStreams = category 
    ? streams.filter(stream => stream.category === category)
    : streams;

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {filteredStreams.map((stream) => (
        <StreamCard key={stream.id} stream={stream} />
      ))}
    </div>
  );
};

export default StreamGrid;
