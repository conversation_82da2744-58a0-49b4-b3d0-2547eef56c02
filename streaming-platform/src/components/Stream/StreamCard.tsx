import React from 'react';
import { Link } from 'react-router-dom';
import { EyeIcon } from '@heroicons/react/24/outline';

interface Stream {
  id: string;
  title: string;
  streamer: string;
  game: string;
  viewers: number;
  thumbnail: string;
  isLive: boolean;
  category: string;
}

interface StreamCardProps {
  stream: Stream;
}

const StreamCard: React.FC<StreamCardProps> = ({ stream }) => {
  const formatViewers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  return (
    <Link to={`/stream/${stream.id}`} className="group animate-fadeIn">
      <div className="card overflow-hidden hover-lift hover-glow transition-all duration-300">
        {/* Thumbnail */}
        <div className="relative aspect-video overflow-hidden">
          <img
            src={stream.thumbnail}
            alt={stream.title}
            className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500"
            loading="lazy"
          />
          {/* Gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

          {stream.isLive && (
            <div className="absolute top-3 left-3 bg-red-600 text-white text-xs font-semibold px-2 py-1 rounded-full flex items-center space-x-1 live-pulse">
              <div className="w-2 h-2 bg-white rounded-full"></div>
              <span>LIVE</span>
            </div>
          )}
          <div className="absolute bottom-3 right-3 glass text-white text-xs px-2 py-1 rounded-full flex items-center space-x-1">
            <EyeIcon className="h-3 w-3" />
            <span>{formatViewers(stream.viewers)}</span>
          </div>
        </div>

        {/* Content */}
        <div className="p-4">
          <div className="flex items-start space-x-3">
            {/* Streamer Avatar */}
            <div className="w-10 h-10 bg-gradient-to-br from-purple-600 to-blue-600 rounded-full flex items-center justify-center flex-shrink-0 shadow-glow">
              <span className="text-sm font-semibold text-white">
                {stream.streamer.charAt(0).toUpperCase()}
              </span>
            </div>

            {/* Stream Info */}
            <div className="flex-1 min-w-0">
              <h3 className="text-white font-medium text-sm line-clamp-2 group-hover:gradient-text transition-all duration-300">
                {stream.title}
              </h3>
              <p className="text-gray-400 text-sm mt-1 group-hover:text-gray-300 transition-colors">
                {stream.streamer}
              </p>
              <p className="text-gray-500 text-xs mt-1">{stream.game}</p>
            </div>
          </div>
        </div>
      </div>
    </Link>
  );
};

export default StreamCard;
