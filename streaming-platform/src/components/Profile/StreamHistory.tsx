import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { PlayIcon, EyeIcon, ClockIcon } from '@heroicons/react/24/outline';

interface StreamHistoryProps {
  username?: string;
}

interface StreamRecord {
  id: string;
  title: string;
  game: string;
  thumbnail: string;
  duration: number; // in minutes
  maxViewers: number;
  averageViewers: number;
  date: Date;
  isHighlight: boolean;
}

const StreamHistory: React.FC<StreamHistoryProps> = ({ username }) => {
  const [activeTab, setActiveTab] = useState<'recent' | 'highlights' | 'clips'>('recent');

  // Mock stream history data with realistic thumbnails
  const streamHistory: StreamRecord[] = [
    {
      id: '1',
      title: 'Epic Gaming Session - Ranked Grind!',
      game: 'Fortnite',
      thumbnail: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=225&fit=crop&crop=center',
      duration: 480, // 8 hours
      maxViewers: 67000,
      averageViewers: 45000,
      date: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
      isHighlight: true
    },
    {
      id: '2',
      title: 'Chill Stream with Viewers',
      game: 'Valorant',
      thumbnail: 'https://images.unsplash.com/photo-1511512578047-dfb367046420?w=400&h=225&fit=crop&crop=center',
      duration: 360, // 6 hours
      maxViewers: 52000,
      averageViewers: 38000,
      date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
      isHighlight: false
    },
    {
      id: '3',
      title: 'New Game First Playthrough!',
      game: 'Cyberpunk 2077',
      thumbnail: 'https://images.unsplash.com/photo-1552820728-8b83bb6b773f?w=400&h=225&fit=crop&crop=center',
      duration: 420, // 7 hours
      maxViewers: 89000,
      averageViewers: 72000,
      date: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
      isHighlight: true
    },
    {
      id: '4',
      title: 'Tournament Practice Stream',
      game: 'Fortnite',
      thumbnail: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=400&h=225&fit=crop&crop=center',
      duration: 300, // 5 hours
      maxViewers: 45000,
      averageViewers: 35000,
      date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000), // 4 days ago
      isHighlight: false
    },
    {
      id: '5',
      title: 'Subscriber Games & Giveaway',
      game: 'Fall Guys',
      thumbnail: 'https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=400&h=225&fit=crop&crop=center',
      duration: 240, // 4 hours
      maxViewers: 78000,
      averageViewers: 65000,
      date: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
      isHighlight: true
    },
    {
      id: '6',
      title: 'Late Night Gaming Session',
      game: 'Minecraft',
      thumbnail: 'https://images.unsplash.com/photo-1606092195730-5d7b9af1efc5?w=400&h=225&fit=crop&crop=center',
      duration: 180, // 3 hours
      maxViewers: 32000,
      averageViewers: 28000,
      date: new Date(Date.now() - 6 * 24 * 60 * 60 * 1000), // 6 days ago
      isHighlight: false
    },
    {
      id: '7',
      title: 'Music Production Live',
      game: 'Music & Performing Arts',
      thumbnail: 'https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=400&h=225&fit=crop&crop=center',
      duration: 300, // 5 hours
      maxViewers: 25000,
      averageViewers: 18000,
      date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
      isHighlight: false
    },
    {
      id: '8',
      title: 'Art Stream - Digital Painting',
      game: 'Art',
      thumbnail: 'https://images.unsplash.com/photo-1541961017774-22349e4a1262?w=400&h=225&fit=crop&crop=center',
      duration: 240, // 4 hours
      maxViewers: 15000,
      averageViewers: 12000,
      date: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000), // 8 days ago
      isHighlight: true
    }
  ];

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  const formatViewers = (count: number) => {
    if (count >= 1000000) {
      return `${(count / 1000000).toFixed(1)}M`;
    } else if (count >= 1000) {
      return `${(count / 1000).toFixed(1)}K`;
    }
    return count.toString();
  };

  const formatDate = (date: Date) => {
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays === 1) return '1 day ago';
    if (diffDays < 7) return `${diffDays} days ago`;
    if (diffDays < 30) return `${Math.ceil(diffDays / 7)} weeks ago`;
    return date.toLocaleDateString();
  };

  const getFilteredStreams = () => {
    switch (activeTab) {
      case 'highlights':
        return streamHistory.filter(stream => stream.isHighlight);
      case 'clips':
        return []; // Would be populated with clip data
      default:
        return streamHistory;
    }
  };

  const filteredStreams = getFilteredStreams();

  return (
    <div>
      {/* Tab Navigation */}
      <div className="flex space-x-6 border-b border-gray-700 mb-6">
        <button
          onClick={() => setActiveTab('recent')}
          className={`pb-3 px-1 border-b-2 font-medium text-sm transition-colors ${
            activeTab === 'recent'
              ? 'border-purple-500 text-white'
              : 'border-transparent text-gray-400 hover:text-white'
          }`}
        >
          Recent Streams
        </button>
        <button
          onClick={() => setActiveTab('highlights')}
          className={`pb-3 px-1 border-b-2 font-medium text-sm transition-colors ${
            activeTab === 'highlights'
              ? 'border-purple-500 text-white'
              : 'border-transparent text-gray-400 hover:text-white'
          }`}
        >
          Highlights
        </button>
        <button
          onClick={() => setActiveTab('clips')}
          className={`pb-3 px-1 border-b-2 font-medium text-sm transition-colors ${
            activeTab === 'clips'
              ? 'border-purple-500 text-white'
              : 'border-transparent text-gray-400 hover:text-white'
          }`}
        >
          Clips
        </button>
      </div>

      {/* Content */}
      {activeTab === 'clips' ? (
        <div className="text-center py-12">
          <p className="text-gray-400">No clips available yet.</p>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredStreams.map((stream) => (
            <div key={stream.id} className="card overflow-hidden hover:scale-105 transition-transform duration-200">
              {/* Thumbnail */}
              <div className="relative aspect-video">
                <img
                  src={stream.thumbnail}
                  alt={stream.title}
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center opacity-0 hover:opacity-100 transition-opacity">
                  <Link
                    to={`/stream/${stream.id}`}
                    className="bg-purple-600 hover:bg-purple-700 text-white p-3 rounded-full transition-colors"
                  >
                    <PlayIcon className="h-6 w-6" />
                  </Link>
                </div>
                
                {stream.isHighlight && (
                  <div className="absolute top-2 left-2 bg-yellow-600 text-white text-xs font-semibold px-2 py-1 rounded">
                    HIGHLIGHT
                  </div>
                )}
                
                <div className="absolute bottom-2 right-2 bg-black bg-opacity-75 text-white text-xs px-2 py-1 rounded flex items-center space-x-1">
                  <ClockIcon className="h-3 w-3" />
                  <span>{formatDuration(stream.duration)}</span>
                </div>
              </div>

              {/* Content */}
              <div className="p-4">
                <h3 className="text-white font-medium text-sm mb-2 line-clamp-2">
                  {stream.title}
                </h3>
                
                <p className="text-gray-400 text-sm mb-3">{stream.game}</p>
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    <EyeIcon className="h-3 w-3" />
                    <span>Peak: {formatViewers(stream.maxViewers)}</span>
                  </div>
                  <span>{formatDate(stream.date)}</span>
                </div>
                
                <div className="mt-2 text-xs text-gray-500">
                  Avg: {formatViewers(stream.averageViewers)} viewers
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default StreamHistory;
