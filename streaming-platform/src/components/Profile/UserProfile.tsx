import React, { useState } from 'react';
import { 
  UserPlusIcon, 
  BellIcon, 
  ShareIcon, 
  CalendarIcon,
  MapPinIcon,
  LinkIcon,
  PlayIcon
} from '@heroicons/react/24/outline';
import { BellIcon as BellSolidIcon } from '@heroicons/react/24/solid';

interface UserProfileProps {
  username?: string;
}

const UserProfile: React.FC<UserProfileProps> = ({ username }) => {
  const [isFollowing, setIsFollowing] = useState(false);
  const [hasNotifications, setHasNotifications] = useState(false);

  // Mock user data - in production this would come from an API
  const userData = {
    username: username || 'ninja',
    displayName: 'Ninja',
    avatar: 'https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?w=200&h=200&fit=crop&crop=face',
    banner: 'https://images.unsplash.com/photo-1542751371-adc38448a05e?w=1200&h=300&fit=crop&crop=center',
    isVerified: true,
    isLive: true,
    currentGame: 'Fortnite',
    currentViewers: 45234,
    followers: 18500000,
    totalViews: 892000000,
    joinDate: new Date('2018-03-15'),
    location: 'United States',
    bio: `Professional gamer and content creator. Streaming daily with high-energy gameplay and educational content.

    🎮 Fortnite World Champion
    🏆 Multiple tournament winner
    📺 Full-time content creator since 2018

    Business inquiries: <EMAIL>

    Thanks for the support! 💜`,
    socialLinks: [
      { platform: 'Twitter', url: 'https://twitter.com/ninja', handle: '@ninja' },
      { platform: 'Instagram', url: 'https://instagram.com/ninja', handle: '@ninja' },
      { platform: 'YouTube', url: 'https://youtube.com/ninja', handle: 'Ninja' },
      { platform: 'TikTok', url: 'https://tiktok.com/@ninja', handle: '@ninja' }
    ],
    schedule: [
      { day: 'Monday', time: '2:00 PM - 8:00 PM EST' },
      { day: 'Tuesday', time: '2:00 PM - 8:00 PM EST' },
      { day: 'Wednesday', time: '2:00 PM - 8:00 PM EST' },
      { day: 'Thursday', time: '2:00 PM - 8:00 PM EST' },
      { day: 'Friday', time: '2:00 PM - 8:00 PM EST' },
      { day: 'Saturday', time: '12:00 PM - 10:00 PM EST' },
      { day: 'Sunday', time: '12:00 PM - 10:00 PM EST' }
    ]
  };

  const formatNumber = (num: number) => {
    if (num >= 1000000000) {
      return `${(num / 1000000000).toFixed(1)}B`;
    } else if (num >= 1000000) {
      return `${(num / 1000000).toFixed(1)}M`;
    } else if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}K`;
    }
    return num.toString();
  };

  const formatJoinDate = (date: Date) => {
    return date.toLocaleDateString('en-US', { 
      year: 'numeric', 
      month: 'long' 
    });
  };

  return (
    <div className="relative">
      {/* Banner */}
      <div className="h-48 md:h-64 relative">
        <img
          src={userData.banner}
          alt="Profile banner"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
      </div>

      {/* Profile Info */}
      <div className="relative px-6 pb-6">
        <div className="flex flex-col md:flex-row md:items-end md:space-x-6 -mt-16 md:-mt-20">
          {/* Avatar */}
          <div className="relative mb-4 md:mb-0">
            <img
              src={userData.avatar}
              alt={userData.displayName}
              className="w-24 h-24 md:w-32 md:h-32 rounded-full border-4 border-dark-300 bg-dark-300"
            />
            {userData.isLive && (
              <div className="absolute -bottom-2 -right-2 bg-red-600 text-white text-xs font-semibold px-2 py-1 rounded-full flex items-center space-x-1">
                <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
                <span>LIVE</span>
              </div>
            )}
          </div>

          {/* User Info */}
          <div className="flex-1">
            <div className="flex flex-col md:flex-row md:items-center md:justify-between">
              <div>
                <div className="flex items-center space-x-2 mb-2">
                  <h1 className="text-2xl md:text-3xl font-bold text-white">
                    {userData.displayName}
                  </h1>
                  {userData.isVerified && (
                    <div className="w-6 h-6 bg-purple-600 rounded-full flex items-center justify-center">
                      <span className="text-white text-sm">✓</span>
                    </div>
                  )}
                </div>
                
                {userData.isLive && (
                  <div className="flex items-center space-x-4 text-sm text-gray-300 mb-2">
                    <div className="flex items-center space-x-1">
                      <PlayIcon className="h-4 w-4 text-red-500" />
                      <span>Playing {userData.currentGame}</span>
                    </div>
                    <span>•</span>
                    <span>{formatNumber(userData.currentViewers)} viewers</span>
                  </div>
                )}

                <div className="flex items-center space-x-6 text-sm text-gray-400">
                  <div>
                    <span className="font-semibold text-white">{formatNumber(userData.followers)}</span>
                    <span className="ml-1">followers</span>
                  </div>
                  <div>
                    <span className="font-semibold text-white">{formatNumber(userData.totalViews)}</span>
                    <span className="ml-1">total views</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center space-x-3 mt-4 md:mt-0">
                <button
                  onClick={() => setIsFollowing(!isFollowing)}
                  className={`flex items-center space-x-2 px-6 py-2 rounded-lg font-medium transition-colors ${
                    isFollowing
                      ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                      : 'bg-purple-600 hover:bg-purple-700 text-white'
                  }`}
                >
                  <UserPlusIcon className="h-5 w-5" />
                  <span>{isFollowing ? 'Following' : 'Follow'}</span>
                </button>

                <button
                  onClick={() => setHasNotifications(!hasNotifications)}
                  className={`p-2 rounded-lg transition-colors ${
                    hasNotifications
                      ? 'bg-purple-600 hover:bg-purple-700 text-white'
                      : 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                  }`}
                >
                  {hasNotifications ? (
                    <BellSolidIcon className="h-5 w-5" />
                  ) : (
                    <BellIcon className="h-5 w-5" />
                  )}
                </button>

                <button className="p-2 bg-gray-700 hover:bg-gray-600 text-gray-300 rounded-lg transition-colors">
                  <ShareIcon className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Additional Info */}
        <div className="mt-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Bio */}
          <div className="lg:col-span-2">
            <h3 className="text-white font-semibold mb-3">About</h3>
            <div className="text-gray-300 text-sm whitespace-pre-line bg-dark-200 rounded-lg p-4">
              {userData.bio}
            </div>

            {/* Social Links */}
            <div className="mt-4">
              <h4 className="text-white font-semibold mb-2">Social Media</h4>
              <div className="flex flex-wrap gap-2">
                {userData.socialLinks.map((link) => (
                  <a
                    key={link.platform}
                    href={link.url}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center space-x-2 bg-dark-200 hover:bg-dark-100 px-3 py-2 rounded-lg text-gray-300 hover:text-white transition-colors"
                  >
                    <LinkIcon className="h-4 w-4" />
                    <span className="text-sm">{link.platform}: {link.handle}</span>
                  </a>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar Info */}
          <div className="space-y-4">
            {/* Join Date & Location */}
            <div className="bg-dark-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 text-gray-300 mb-2">
                <CalendarIcon className="h-4 w-4" />
                <span className="text-sm">Joined {formatJoinDate(userData.joinDate)}</span>
              </div>
              <div className="flex items-center space-x-2 text-gray-300">
                <MapPinIcon className="h-4 w-4" />
                <span className="text-sm">{userData.location}</span>
              </div>
            </div>

            {/* Schedule */}
            <div className="bg-dark-200 rounded-lg p-4">
              <h4 className="text-white font-semibold mb-3">Schedule</h4>
              <div className="space-y-2">
                {userData.schedule.map((item) => (
                  <div key={item.day} className="flex justify-between text-sm">
                    <span className="text-gray-400">{item.day}</span>
                    <span className="text-gray-300">{item.time}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserProfile;
