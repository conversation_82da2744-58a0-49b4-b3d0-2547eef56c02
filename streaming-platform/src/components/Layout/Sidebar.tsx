import React from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  HomeIcon,
  FireIcon,
  HeartIcon,
  UserGroupIcon,
  DevicePhoneMobileIcon,
  MusicalNoteIcon,
  PaintBrushIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

const Sidebar: React.FC = () => {
  const categories = [
    { name: 'Gaming', icon: DevicePhoneMobileIcon, path: '/category/gaming', viewers: '1.2M' },
    { name: 'Music', icon: MusicalNoteIcon, path: '/category/music', viewers: '234K' },
    { name: 'Art', icon: PaintBrushIcon, path: '/category/art', viewers: '89K' },
    { name: 'Talk Shows', icon: ChatBubbleLeftRightIcon, path: '/category/talk', viewers: '156K' },
  ];

  const followedStreamers = [
    { username: 'ninja', isLive: true, game: 'Fortnite', viewers: 45000 },
    { username: 'poki<PERSON><PERSON>', isLive: true, game: 'Valorant', viewers: 23000 },
    { username: 'shroud', isLive: false, game: '', viewers: 0 },
    { username: 'xqc', isLive: true, game: 'GTA V', viewers: 67000 },
  ];

  return (
    <aside className="fixed left-0 top-16 w-60 h-full glass border-r border-gray-700 overflow-y-auto shadow-elegant">
      <div className="p-4">
        {/* Main Navigation */}
        <nav className="space-y-2 mb-6">
          <Link to="/" className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-300 hover:bg-dark-100 hover:text-white transition-colors">
            <HomeIcon className="h-5 w-5" />
            <span>Home</span>
          </Link>
          <Link to="/trending" className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-300 hover:bg-dark-100 hover:text-white transition-colors">
            <FireIcon className="h-5 w-5" />
            <span>Trending</span>
          </Link>
          <Link to="/categories" className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-300 hover:bg-dark-100 hover:text-white transition-colors">
            <UserGroupIcon className="h-5 w-5" />
            <span>Browse</span>
          </Link>
          <Link to="/following" className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-300 hover:bg-dark-100 hover:text-white transition-colors">
            <HeartIcon className="h-5 w-5" />
            <span>Following</span>
          </Link>
        </nav>

        {/* Categories */}
        <div className="mb-6">
          <h3 className="text-gray-400 text-sm font-semibold mb-3 px-3">CATEGORIES</h3>
          <div className="space-y-1">
            {categories.map((category) => (
              <Link
                key={category.name}
                to={category.path}
                className="flex items-center justify-between px-3 py-2 rounded-lg text-gray-300 hover:bg-dark-100 hover:text-white transition-colors"
              >
                <div className="flex items-center space-x-3">
                  <category.icon className="h-5 w-5" />
                  <span className="text-sm">{category.name}</span>
                </div>
                <span className="text-xs text-gray-500">{category.viewers}</span>
              </Link>
            ))}
          </div>
        </div>

        {/* Followed Channels */}
        <div>
          <h3 className="text-gray-400 text-sm font-semibold mb-3 px-3">FOLLOWED CHANNELS</h3>
          <div className="space-y-1">
            {followedStreamers.map((streamer) => (
              <Link
                key={streamer.username}
                to={`/profile/${streamer.username}`}
                className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-300 hover:bg-dark-100 hover:text-white transition-colors"
              >
                <div className="relative">
                  <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center">
                    <span className="text-xs font-semibold">
                      {streamer.username.charAt(0).toUpperCase()}
                    </span>
                  </div>
                  {streamer.isLive && (
                    <div className="absolute -bottom-1 -right-1 w-3 h-3 bg-red-500 rounded-full border-2 border-dark-200"></div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium truncate">{streamer.username}</p>
                  {streamer.isLive ? (
                    <p className="text-xs text-gray-500 truncate">{streamer.game}</p>
                  ) : (
                    <p className="text-xs text-gray-500">Offline</p>
                  )}
                </div>
                {streamer.isLive && (
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                    <span className="text-xs text-gray-500">
                      {streamer.viewers > 1000 
                        ? `${(streamer.viewers / 1000).toFixed(1)}K` 
                        : streamer.viewers}
                    </span>
                  </div>
                )}
              </Link>
            ))}
          </div>
        </div>
      </div>
    </aside>
  );
};

export default Sidebar;
